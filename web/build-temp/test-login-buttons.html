<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第三方登录按钮样式预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            padding: 40px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
        }
        
        h2 {
            color: #666;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        
        .button-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 40px;
        }
        
        .login-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            width: 100%;
            height: 48px;
            padding: 0 16px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-decoration: none;
        }
        
        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .login-button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .login-button img {
            width: 24px;
            height: 24px;
        }
        
        /* 各服务商主题色 */
        .github {
            background-color: #333333;
            color: #ffffff;
        }
        .github:hover {
            background-color: #393934;
        }
        
        .google {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #dadce0;
        }
        .google:hover {
            background-color: #eff0ee;
        }
        
        .wechat {
            background-color: rgb(0,194,80);
            color: #ffffff;
        }
        .wechat:hover {
            background-color: rgb(0,158,64);
        }
        
        .qq {
            background-color: rgb(94,188,249);
            color: #ffffff;
        }
        .qq:hover {
            background-color: rgb(76,143,208);
        }
        
        .facebook {
            background-color: #3b5998;
            color: #ffffff;
        }
        .facebook:hover {
            background-color: #2b3f65;
        }
        
        .weibo {
            background-color: #e62329;
            color: #ffffff;
        }
        .weibo:hover {
            background-color: #e54329;
        }
        
        .alipay {
            background-color: #1677ff;
            color: #ffffff;
        }
        .alipay:hover {
            background-color: #0958d9;
        }
        
        .dingtalk {
            background-color: #0191e0;
            color: #ffffff;
        }
        .dingtalk:hover {
            background-color: rgb(76,143,208);
        }
        
        .comparison {
            display: flex;
            gap: 40px;
            align-items: flex-start;
        }
        
        .comparison > div {
            flex: 1;
        }
        
        .old-style {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .old-style img {
            width: 40px;
            height: 40px;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .old-style img:hover {
            background-color: #f0f0f0;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>第三方登录按钮样式预览</h1>
        
        <div class="comparison">
            <div>
                <h2>新样式（统一风格）</h2>
                <div class="button-group">
                    <button class="login-button github">
                        <svg height="24" width="24" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"/>
                        </svg>
                        <span>GitHub</span>
                    </button>
                    
                    <button class="login-button google">
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span>Google</span>
                    </button>
                    
                    <button class="login-button wechat">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.18-2.641 1.594-4.85 4.479-5.469-.01-.15-.018-.3-.018-.454 0-4.053-3.89-7.341-8.69-7.341l-.003.004zm5.972 5.31c-1.393-.188-2.796.064-3.926.758a5.068 5.068 0 0 0-2.787 3.193 4.518 4.518 0 0 0 .103 2.453c.396 1.28 1.267 2.35 2.453 3.012a5.14 5.14 0 0 0 3.003.647c.762-.024 1.517-.219 2.205-.579.604-.316 1.13-.772 1.52-1.346.724-1.067.973-2.419.642-3.692a4.522 4.522 0 0 0-3.215-3.446h.002zm-8.007-.3a1.13 1.13 0 1 1 0 2.26 1.13 1.13 0 0 1 0-2.26zm4.003 0a1.13 1.13 0 1 1 0 2.26 1.13 1.13 0 0 1 0-2.26z"/>
                        </svg>
                        <span>微信</span>
                    </button>
                    
                    <button class="login-button qq">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.04 2 11c0 2.52 1.1 4.8 2.84 6.44-.17.66-.38 1.28-.38 1.28s1.14-.4 2.16-1.04c.93.36 1.94.56 3 .56 5.52 0 10-4.04 10-9s-4.48-9-10-9z"/>
                        </svg>
                        <span>QQ</span>
                    </button>
                    
                    <button class="login-button facebook">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        <span>Facebook</span>
                    </button>
                    
                    <button class="login-button weibo">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10.098 20.323c-3.977.391-7.414-1.406-7.672-4.02-.259-2.609 2.759-5.047 6.74-5.441 3.979-.394 7.413 1.404 7.671 4.018.259 2.6-2.759 5.049-6.737 5.439l-.002.004zM9.05 17.219c-.384.616-1.208.884-1.829.602-.612-.279-.793-.991-.406-1.593.379-.595 1.176-.861 1.793-.601.622.263.82.972.442 1.592zm1.27-1.627c-.141.237-.449.353-.689.253-.236-.09-.313-.361-.177-.586.138-.227.436-.346.672-.24.239.09.315.36.18.601l.014-.028zm.176-2.719c-1.893-.493-4.033.45-4.857 2.118-.836 1.704-.026 3.591 1.886 4.21 1.983.64 4.318-.341 5.132-2.179.8-1.793-.201-3.642-2.161-4.149zm7.563-1.224c-.346-.105-.57-.18-.405-.615.375-.977.412-1.806.005-2.403-.766-1.062-2.862-1.011-5.264-.023 0 0-.753.331-.56-.27.368-1.19.31-2.186-.27-2.762-.128-.127-.211-.184-.270-.213l-.118-.061c-.547-.255-1.5-.49-2.796-.096-1.651.501-3.367 2.009-4.416 3.834-.794 1.382-1.254 2.672-1.254 3.854 0 2.275 2.923 3.657 5.782 3.657 3.75 0 6.239-2.179 6.239-3.907 0-1.044-.876-1.636-1.665-1.865zM19.75 6.622c.784-.824 1.235-1.909 1.235-3.113 0-.359-.045-.709-.127-1.043a.594.594 0 00-1.141.321c.063.273.099.557.099.854 0 .968-.361 1.862-.981 2.534a3.359 3.359 0 01-2.584.998c-.28-.023-.568.074-.712.315a.596.596 0 00.054.746.596.596 0 00.435.194l.353-.007a4.63 4.63 0 003.368-1.799h.001z"/>
                        </svg>
                        <span>微博</span>
                    </button>
                    
                    <button class="login-button alipay">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 12.639c-1.856-1.906-3.863-3.363-6.118-4.439 1.244-.988 2.248-2.141 2.916-3.421a.371.371 0 01.201-.189c.487-.024 1.211-.044 2.633-.044.098 0 .177.08.177.178v.957c0 .98.798 1.777 1.778 1.777h.236V4.444A2.222 2.222 0 0018.6 2.222H5.4a2.222 2.222 0 00-2.223 2.222v15.112A2.222 2.222 0 005.4 21.778h13.2a2.222 2.222 0 002.223-2.222v-6.217c-.769.364-1.557.702-2.361 1.013l-.462.287zm-7.56-4.608c.313.031.62.068.917.112a12.903 12.903 0 00-.799-1.858h-.83v-.889h2.489v-.445h-2.49v-.889h2.49v-.445H8.78v.445h2.489v.889H8.78v.445h2.133v.889h-1.83v.445h1.663c-.316.751-.763 1.424-1.313 1.964a20.069 20.069 0 00-2.185-.224 4.43 4.43 0 00-2.74.975c-.786.622-1.18 1.485-1.18 2.59 0 .414.07.812.211 ***********.354.723.638 1.029.284.305.64.548 1.069.729.428.18.926.27 1.495.27.568 0 1.095-.098 1.58-.293a4.776 4.776 0 001.28-.802 5.266 5.266 0 00.912-1.16 6.4 6.4 0 00.55-1.345c.757.44 1.475.947 2.147 1.523-1.335 1.273-2.911 2.16-4.857 2.16-1.447 0-2.765-.52-3.955-1.557a5.254 5.254 0 01-1.781-3.942c0-1.431.535-2.753 1.604-3.969 1.07-1.216 2.485-1.823 4.246-1.823.396 0 .783.027 1.16.081.376.055.74.13 1.09.227V4.889h-3.2v-.445h7.636v.445h-3.547v2.142h2.933v.445h-2.933v.556zm-2.311 9.508c-1.107 0-2.027-.309-2.76-.925-.733-.617-1.1-1.386-1.1-2.308 0-.922.337-1.644 1.01-2.165.674-.522 1.423-.783 2.248-.783.824 0 1.622.13 2.392.392-.154.73-.437 1.382-.85 1.955a4.428 4.428 0 01-1.458 1.393c-.579.293-1.107.44-1.584.44h.102z"/>
                        </svg>
                        <span>支付宝</span>
                    </button>
                    
                    <button class="login-button dingtalk">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5.5 10c0 .28-.22.5-.5.5h-2.5v2.5c0 .28-.22.5-.5.5s-.5-.22-.5-.5v-2.5H11c-.28 0-.5-.22-.5-.5s.22-.5.5-.5h2.5V9c0-.28.22-.5.5-.5s.5.22.5.5v2.5H17c.28 0 .5.22.5.5z"/>
                        </svg>
                        <span>钉钉</span>
                    </button>
                </div>
            </div>
            
            <div>
                <h2>旧样式（仅图标）</h2>
                <div class="old-style">
                    <img src="data:image/svg+xml;base64,PHN2ZyBoZWlnaHQ9IjI0IiB3aWR0aD0iMjQiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0iIzMzMzMzMyI+PHBhdGggZD0iTTggMEMzLjU4IDAgMCAzLjU4IDAgOGMwIDMuNTQgMi4yOSA2LjUzIDUuNDcgNy41OS40LjA3LjU1LS4xNy41NS0uMzggMC0uMTktLjAxLS44Mi0uMDEtMS40OS0yLjAxLjM3LTIuNTMtLjQ5LTIuNjktLjk0LS4wOS0uMjMtLjQ4LS45NC0uODItMS4xMy0uMjgtLjE1LS42OC0uNTItLjAxLS41My42My0uMDEgMS4wOC41OCAxLjIzLjgyLjcyIDEuMjEgMS44Ny44NyAyLjMzLjY2LjA3LS41Mi4yOC0uODcuNTEtMS4wNy0xLjc4LS4yLTMuNjQtLjg5LTMuNjQtMy45NSAwLS44Ny4zMS0xLjU5LjgyLTIuMTUtLjA4LS4yLS4zNi0xLjAyLjA4LTIuMTIgMCAwIC42Ny0uMjEgMi4yLjgyLjY0LS4xOCAxLjMyLS4yNyAyLS4yNy42OCAwIDEuMzYuMDkgMiAuMjcgMS41My0xLjA0IDIuMi0uODIgMi4yLS44Mi40NCAxLjEuMTYgMS45Mi4wOCAyLjEyLjUxLjU2LjgyIDEuMjcuODIgMi4xNSAwIDMuMDctMS44NyAzLjc1LTMuNjUgMy45NS4yOS4yNS41NC43My41NCAxLjQ4IDAgMS4wNy0uMDEgMS45My0uMDEgMi4yIDAgLjIxLjE1LjQ2LjU1LjM4QTguMDEzIDguMDEzIDAgMDAxNiA4YzAtNC40Mi0zLjU4LTgtOC04eiIvPjwvc3ZnPg==" alt="GitHub">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzQyODVGNCIgZD0iTTIyLjU2IDEyLjI1YzAtLjc4LS4wNy0xLjUzLS4yLTIuMjVIMTJ2NC4yNmg1LjkyYy0uMjYgMS4zNy0xLjA0IDIuNTMtMi4yMSAzLjMxdjIuNzdoMy41N2MyLjA4LTEuOTIgMy4yOC00Ljc0IDMuMjgtOC4wOXoiLz48cGF0aCBmaWxsPSIjMzRBODUzIiBkPSJNMTIgMjNjMi45NyAwIDUuNDYtLjk4IDcuMjgtMi42NmwtMy41Ny0yLjc3Yy0uOTguNjYtMi4yMyAxLjA2LTMuNzEgMS4wNi0yLjg2IDAtNS4yOS0xLjkzLTYuMTYtNC41M0gyLjE4djIuODRDMy45OSAyMC41MyA3LjcgMjMgMTIgMjN6Ii8+PHBhdGggZmlsbD0iI0ZCQkMwNSIgZD0iTTUuODQgMTQuMDljLS4yMi0uNjYtLjM1LTEuMzYtLjM1LTIuMDlzLjEzLTEuNDMuMzUtMi4wOVY3LjA3SDIuMThDMS40MyA4LjU1IDEgMTAuMjIgMSAxMnMuNDMgMy40NSAxLjE4IDQuOTNsMi44NS0yLjIyLjgxLS42MnoiLz48cGF0aCBmaWxsPSIjRUE0MzM1IiBkPSJNMTIgNS4zOGMxLjYyIDAgMy4wNi41NiA0LjIxIDEuNjRsMy4xNS0zLjE1QzE3LjQ1IDIuMDkgMTQuOTcgMSAxMiAxIDcuNyAxIDMuOTkgMy40NyAyLjE4IDcuMDdsMy42NiAyLjg0Yy44Ny0yLjYgMy4zLTQuNTMgNi4xNi00LjUzeiIvPjwvc3ZnPg==" alt="Google">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzAwQzI1MCI+PHBhdGggZD0iTTguNjkxIDIuMTg4QzMuODkxIDIuMTg4IDAgNS40NzYgMCA5LjUzYzAgMi4yMTIgMS4xNyA0LjIwMyAzLjAwMiA1LjU1YS41OS41OSAwIDAgMSAuMjEzLjY2NWwtLjM5IDEuNDhjLS4wMTkuMDctLjA0OC4xNDEtLjA0OC4yMTMgMCAuMTYzLjEzLjI5NS4yOS4yOTVhLjMyNi4zMjYgMCAwIDAgLjE2Ny0uMDU0bDEuOTAzLTEuMTE0YS44NjQuODY0IDAgMCAxIC43MTctLjA5OCAxMC4xNiAxMC4xNiAwIDAgMCAyLjgzNy40MDNjLjI3NiAwIC41NDMtLjAyNy44MTEtLjA1LS4xOC0yLjY0MSAxLjU5NC00Ljg1IDQuNDc5LTUuNDY5LS4wMS0uMTUtLjAxOC0uMy0uMDE4LS40NTQgMC00LjA1My0zLjg5LTcuMzQxLTguNjktNy4zNDFsLS4wMDMuMDA0em01Ljk3MiA1LjMxYy0xLjM5My0uMTg4LTIuNzk2LjA2NC0zLjkyNi43NThhNS4wNjggNS4wNjggMCAwIDAtMi43ODcgMy4xOTMgNC41MTggNC41MTggMCAwIDAgLjEwMyAyLjQ1M2MuMzk2IDEuMjggMS4yNjcgMi4zNSAyLjQ1MyAzLjAxMmE1LjE0IDUuMTQgMCAwIDAgMy4wMDMuNjQ3Yy43NjItLjAyNCAxLjUxNy0uMjE5IDIuMjA1LS41NzkuNjA0LS4zMTYgMS4xMy0uNzcyIDEuNTItMS4zNDYuNzI0LTEuMDY3Ljk3My0yLjQxOS42NDItMy42OTJhNC41MjIgNC41MjIgMCAwIDAtMy4yMTUtMy40NDZoLjAwMnptLTguMDA3LS4zYTEuMTMgMS4xMyAwIDEgMSAwIDIuMjYgMS4xMyAxLjEzIDAgMCAxIDAtMi4yNnptNC4wMDMgMGExLjEzIDEuMTMgMCAxIDEgMCAyLjI2IDEuMTMgMS4xMyAwIDAgMSAwLTIuMjZ6Ii8+PC9zdmc+" alt="微信">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdC