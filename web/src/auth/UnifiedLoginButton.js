// Copyright 2025 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React from "react";
import {createButton} from "react-social-login-buttons";
import {getProviderLogoURL} from "../Setting";
import {getProviderTheme} from "./ProviderThemes";
import i18next from "i18next";

function UnifiedLoginButton({provider, text, align = "center"}) {
  const theme = getProviderTheme(provider.type);
  const displayName = provider.displayName !== "" ? provider.displayName : theme.displayName || provider.type;
  
  function Icon({width = 24, height = 24}) {
    return (
      <img
        src={getProviderLogoURL(provider)}
        alt={`Sign in with ${displayName}`}
        style={{width: width, height: height}}
      />
    );
  }

  // Use the display name in the button text
  const buttonText = i18next.t("login:Sign in with {type}").replace("{type}", displayName);

  const config = {
    text: buttonText,
    icon: Icon,
    iconFormat: name => `fa fa-${name}`,
    style: {
      background: theme.background,
      color: theme.color,
      fontWeight: "bold",
      fontSize: "16px",
      width: "100%",
      height: "48px",
      margin: "8px 0",
      padding: "0 16px",
      border: "none",
      borderRadius: "6px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      transition: "all 0.3s ease",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    },
    activeStyle: {
      background: theme.activeBackground,
      transform: "translateY(1px)",
      boxShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
    },
  };

  const Button = createButton(config);
  
  return <Button text={buttonText} align={align} />;
}

export default UnifiedLoginButton;