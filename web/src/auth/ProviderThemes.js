// Copyright 2025 The Casdoor Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Provider theme colors configuration
export const providerThemes = {
  GitHub: {
    background: "#333333",
    activeBackground: "#393934",
    color: "#ffffff",
    displayName: "GitHub"
  },
  Google: {
    background: "#ffffff",
    activeBackground: "#eff0ee",
    color: "#000000",
    displayName: "Google"
  },
  QQ: {
    background: "rgb(94,188,249)",
    activeBackground: "rgb(76,143,208)",
    color: "#ffffff",
    displayName: "QQ"
  },
  Facebook: {
    background: "#3b5998",
    activeBackground: "#2b3f65",
    color: "#ffffff",
    displayName: "Facebook"
  },
  Weibo: {
    background: "#e62329",
    activeBackground: "#e54329",
    color: "#ffffff",
    displayName: "微博"
  },
  Gitee: {
    background: "rgb(199,29,35)",
    activeBackground: "rgb(147,22,26)",
    color: "#ffffff",
    displayName: "Gitee"
  },
  WeChat: {
    background: "rgb(0,194,80)",
    activeBackground: "rgb(0,158,64)",
    color: "#ffffff",
    displayName: "微信"
  },
  DingTalk: {
    background: "#0191e0",
    activeBackground: "rgb(76,143,208)",
    color: "#ffffff",
    displayName: "钉钉"
  },
  LinkedIn: {
    background: "rgb(255,255,255)",
    activeBackground: "rgb(240,240,250)",
    color: "#000000",
    displayName: "LinkedIn"
  },
  WeCom: {
    background: "#2d8cf0",
    activeBackground: "#2b85e4",
    color: "#ffffff",
    displayName: "企业微信"
  },
  Lark: {
    background: "#00d6b9",
    activeBackground: "#00c4a7",
    color: "#ffffff",
    displayName: "飞书"
  },
  GitLab: {
    background: "#fc6d26",
    activeBackground: "#e24329",
    color: "#ffffff",
    displayName: "GitLab"
  },
  ADFS: {
    background: "#0078d4",
    activeBackground: "#106ebe",
    color: "#ffffff",
    displayName: "ADFS"
  },
  Casdoor: {
    background: "#6a5acd",
    activeBackground: "#5a4abd",
    color: "#ffffff",
    displayName: "Casdoor"
  },
  Baidu: {
    background: "#2932e1",
    activeBackground: "#1e28c7",
    color: "#ffffff",
    displayName: "百度"
  },
  Alipay: {
    background: "#1677ff",
    activeBackground: "#0958d9",
    color: "#ffffff",
    displayName: "支付宝"
  },
  Infoflow: {
    background: "#40a9ff",
    activeBackground: "#1890ff",
    color: "#ffffff",
    displayName: "Infoflow"
  },
  Apple: {
    background: "#000000",
    activeBackground: "#1a1a1a",
    color: "#ffffff",
    displayName: "Apple"
  },
  AzureAD: {
    background: "#0078d4",
    activeBackground: "#106ebe",
    color: "#ffffff",
    displayName: "Azure AD"
  },
  AzureADB2C: {
    background: "#0078d4",
    activeBackground: "#106ebe",
    color: "#ffffff",
    displayName: "Azure AD B2C"
  },
  Slack: {
    background: "#4a154b",
    activeBackground: "#611f69",
    color: "#ffffff",
    displayName: "Slack"
  },
  Steam: {
    background: "#171a21",
    activeBackground: "#2a475e",
    color: "#ffffff",
    displayName: "Steam"
  },
  Bilibili: {
    background: "#fb7299",
    activeBackground: "#f25d8e",
    color: "#ffffff",
    displayName: "哔哩哔哩"
  },
  Okta: {
    background: "#007dc1",
    activeBackground: "#0065a0",
    color: "#ffffff",
    displayName: "Okta"
  },
  Douyin: {
    background: "#000000",
    activeBackground: "#1a1a1a",
    color: "#ffffff",
    displayName: "抖音"
  },
  Kwai: {
    background: "#ff6f00",
    activeBackground: "#ff5722",
    color: "#ffffff",
    displayName: "快手"
  },
  // Default theme for unknown providers
  Default: {
    background: "#6a5acd",
    activeBackground: "#5a4abd",
    color: "#ffffff",
    displayName: ""
  }
};

// Get theme for a specific provider
export function getProviderTheme(providerType) {
  return providerThemes[providerType] || providerThemes.Default;
}